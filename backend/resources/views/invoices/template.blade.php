<!DOCTYPE html>
<html lang="ka">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ინვოისი #{{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            float: left;
            width: 50%;
        }
        
        .invoice-info {
            float: right;
            width: 45%;
            text-align: right;
        }
        
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        
        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .invoice-number {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        
        .addresses {
            margin: 30px 0;
        }
        
        .address-block {
            float: left;
            width: 48%;
            margin-bottom: 20px;
        }
        
        .address-title {
            font-weight: bold;
            font-size: 14px;
            color: #374151;
            margin-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .table th,
        .table td {
            border: 1px solid #e5e7eb;
            padding: 12px 8px;
            text-align: left;
        }
        
        .table th {
            background-color: #f9fafb;
            font-weight: bold;
            color: #374151;
        }
        
        .table .text-right {
            text-align: right;
        }
        
        .table .text-center {
            text-align: center;
        }
        
        .totals {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals td {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .totals .total-row {
            font-weight: bold;
            font-size: 16px;
            background-color: #f3f4f6;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 11px;
            color: #6b7280;
        }
        
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9fafb;
            border-left: 4px solid #3b82f6;
        }
        
        .notes-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header clearfix">
        <div class="company-info">
            <div class="company-name">{{ $company['name'] ?? 'თქვენი კომპანია' }}</div>
            <div>{{ $company['address'] ?? '' }}</div>
            <div>{{ $company['phone'] ?? '' }}</div>
            <div>{{ $company['email'] ?? '' }}</div>
            @if(isset($company['tax_number']))
                <div>საგადასახადო ნომერი: {{ $company['tax_number'] }}</div>
            @endif
        </div>
        
        <div class="invoice-info">
            <div class="invoice-title">ინვოისი</div>
            <div class="invoice-number"># {{ $invoice->invoice_number }}</div>
            <div>გამოცემის თარიღი: {{ $invoice->issue_date->format('d/m/Y') }}</div>
            <div>გადახდის ვადა: {{ $invoice->due_date->format('d/m/Y') }}</div>
            @if($invoice->status === 'paid' && $invoice->paid_date)
                <div style="color: #059669; font-weight: bold;">
                    გადახდილია: {{ $invoice->paid_date->format('d/m/Y') }}
                </div>
            @endif
        </div>
    </div>

    <div class="addresses clearfix">
        <div class="address-block">
            <div class="address-title">მყიდველი:</div>
            <div><strong>{{ $customer['name'] ?? 'N/A' }}</strong></div>
            @if(isset($customer['company_name']))
                <div>{{ $customer['company_name'] }}</div>
            @endif
            <div>{{ $customer['email'] ?? '' }}</div>
            <div>{{ $customer['phone'] ?? '' }}</div>
            @if(isset($customer['address']))
                <div>{{ $customer['address'] }}</div>
            @endif
        </div>
        
        <div class="address-block" style="float: right;">
            <div class="address-title">შეკვეთის ინფორმაცია:</div>
            <div>შეკვეთის ნომერი: {{ $order->order_number }}</div>
            <div>შეკვეთის თარიღი: {{ $order->order_date->format('d/m/Y') }}</div>
            <div>ტიპი: {{ $order->type === 'wholesale' ? 'საბითუმო' : 'საცალო' }}</div>
            <div>სტატუსი: {{ ucfirst($order->status) }}</div>
        </div>
    </div>

    <table class="table">
        <thead>
            <tr>
                <th style="width: 5%;">#</th>
                <th style="width: 15%;">კოდი</th>
                <th style="width: 40%;">პროდუქტი</th>
                <th style="width: 10%;" class="text-center">რაოდენობა</th>
                <th style="width: 15%;" class="text-right">ფასი</th>
                <th style="width: 15%;" class="text-right">ჯამი</th>
            </tr>
        </thead>
        <tbody>
            @foreach($order->items as $index => $item)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $item->product_sku }}</td>
                    <td>{{ $item->product_name }}</td>
                    <td class="text-center">{{ $item->quantity }}</td>
                    <td class="text-right">{{ number_format($item->unit_price, 2) }} ₾</td>
                    <td class="text-right">{{ number_format($item->total_price, 2) }} ₾</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="totals">
        <table>
            <tr>
                <td>ქვეჯამი:</td>
                <td class="text-right">{{ number_format($invoice->subtotal, 2) }} ₾</td>
            </tr>
            @if($invoice->discount_amount > 0)
                <tr>
                    <td>ფასდაკლება:</td>
                    <td class="text-right">-{{ number_format($invoice->discount_amount, 2) }} ₾</td>
                </tr>
            @endif
            @if($invoice->tax_amount > 0)
                <tr>
                    <td>გადასახადი:</td>
                    <td class="text-right">{{ number_format($invoice->tax_amount, 2) }} ₾</td>
                </tr>
            @endif
            <tr class="total-row">
                <td>სულ გადასახდელი:</td>
                <td class="text-right">{{ number_format($invoice->total_amount, 2) }} ₾</td>
            </tr>
        </table>
    </div>

    <div class="clearfix"></div>

    @if($invoice->notes)
        <div class="notes">
            <div class="notes-title">შენიშვნები:</div>
            <div>{{ $invoice->notes }}</div>
        </div>
    @endif

    <div class="footer">
        <div>ინვოისი გენერირებულია {{ now()->format('d/m/Y H:i') }}-ზე</div>
        <div>გმადლობთ თანამშრომლობისთვის!</div>
    </div>
</body>
</html>
