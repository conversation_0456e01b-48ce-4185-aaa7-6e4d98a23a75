{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "autoprefixer": "^10.4.21", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^6.2.4"}, "dependencies": {"@heroicons/react": "^2.2.0", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "react-i18next": "^15.6.0"}}