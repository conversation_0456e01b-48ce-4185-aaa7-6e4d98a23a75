export declare enum charCodes {
    backSpace = 8,
    lineFeed = 10,
    tab = 9,
    carriageReturn = 13,
    shiftOut = 14,
    space = 32,
    exclamationMark = 33,
    quotationMark = 34,
    numberSign = 35,
    dollarSign = 36,
    percentSign = 37,
    ampersand = 38,
    apostrophe = 39,
    leftParenthesis = 40,
    rightParenthesis = 41,
    asterisk = 42,
    plusSign = 43,
    comma = 44,
    dash = 45,
    dot = 46,
    slash = 47,
    digit0 = 48,
    digit1 = 49,
    digit2 = 50,
    digit3 = 51,
    digit4 = 52,
    digit5 = 53,
    digit6 = 54,
    digit7 = 55,
    digit8 = 56,
    digit9 = 57,
    colon = 58,
    semicolon = 59,
    lessThan = 60,
    equalsTo = 61,
    greaterThan = 62,
    questionMark = 63,
    atSign = 64,
    uppercaseA = 65,
    uppercaseB = 66,
    uppercaseC = 67,
    uppercaseD = 68,
    uppercaseE = 69,
    uppercaseF = 70,
    uppercaseG = 71,
    uppercaseH = 72,
    uppercaseI = 73,
    uppercaseJ = 74,
    uppercaseK = 75,
    uppercaseL = 76,
    uppercaseM = 77,
    uppercaseN = 78,
    uppercaseO = 79,
    uppercaseP = 80,
    uppercaseQ = 81,
    uppercaseR = 82,
    uppercaseS = 83,
    uppercaseT = 84,
    uppercaseU = 85,
    uppercaseV = 86,
    uppercaseW = 87,
    uppercaseX = 88,
    uppercaseY = 89,
    uppercaseZ = 90,
    leftSquareBracket = 91,
    backslash = 92,
    rightSquareBracket = 93,
    caret = 94,
    underscore = 95,
    graveAccent = 96,
    lowercaseA = 97,
    lowercaseB = 98,
    lowercaseC = 99,
    lowercaseD = 100,
    lowercaseE = 101,
    lowercaseF = 102,
    lowercaseG = 103,
    lowercaseH = 104,
    lowercaseI = 105,
    lowercaseJ = 106,
    lowercaseK = 107,
    lowercaseL = 108,
    lowercaseM = 109,
    lowercaseN = 110,
    lowercaseO = 111,
    lowercaseP = 112,
    lowercaseQ = 113,
    lowercaseR = 114,
    lowercaseS = 115,
    lowercaseT = 116,
    lowercaseU = 117,
    lowercaseV = 118,
    lowercaseW = 119,
    lowercaseX = 120,
    lowercaseY = 121,
    lowercaseZ = 122,
    leftCurlyBrace = 123,
    verticalBar = 124,
    rightCurlyBrace = 125,
    tilde = 126,
    nonBreakingSpace = 160,
    oghamSpaceMark = 5760,
    lineSeparator = 8232,
    paragraphSeparator = 8233
}
export declare function isDigit(code: number): boolean;
