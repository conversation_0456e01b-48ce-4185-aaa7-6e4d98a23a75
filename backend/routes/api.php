<?php

use App\Http\Controllers\Api\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/me', [AuthController::class, 'me']);
    Route::post('/logout', [AuthController::class, 'logout']);
    
    // User routes will be added here
    Route::prefix('users')->group(function () {
        // User management routes
    });
    
    // Product routes will be added here
    Route::prefix('products')->group(function () {
        // Product management routes
    });
    
    // Order routes will be added here
    Route::prefix('orders')->group(function () {
        // Order management routes
    });
    
    // Invoice routes will be added here
    Route::prefix('invoices')->group(function () {
        // Invoice management routes
    });
    
    // CRM routes will be added here
    Route::prefix('crm')->group(function () {
        // CRM routes
    });
    
    // Analytics routes will be added here
    Route::prefix('analytics')->group(function () {
        // Analytics routes
    });
});
