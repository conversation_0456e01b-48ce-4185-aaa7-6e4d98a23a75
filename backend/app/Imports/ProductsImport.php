<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\Category;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Illuminate\Support\Str;

class ProductsImport implements ToModel, WithHeadingRow, WithValidation
{
    use Importable;

    protected $userId;

    public function __construct($userId)
    {
        $this->userId = $userId;
    }

    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        // Skip empty rows
        if (empty($row['dasaxeleba']) && empty($row['name'])) {
            return null;
        }

        // Get or create category
        $categoryId = null;
        $categoryName = $row['kategoria'] ?? $row['category'] ?? null;
        if ($categoryName) {
            $category = Category::firstOrCreate(
                ['name' => $categoryName, 'user_id' => $this->userId],
                ['slug' => Str::slug($categoryName)]
            );
            $categoryId = $category->id;
        }

        // Generate SKU if not provided
        $sku = $row['kodi_sku'] ?? $row['sku'] ?? $this->generateSku($row['dasaxeleba'] ?? $row['name']);

        return new Product([
            'name' => $row['dasaxeleba'] ?? $row['name'],
            'sku' => $sku,
            'barcode' => $row['barkodi'] ?? $row['barcode'] ?? null,
            'category_id' => $categoryId,
            'price' => $this->parsePrice($row['fasi'] ?? $row['price'] ?? 0),
            'wholesale_price' => $this->parsePrice($row['sabithumo_fasi'] ?? $row['wholesale_price'] ?? 0),
            'cost_price' => $this->parsePrice($row['tvitghirebulebha'] ?? $row['cost_price'] ?? 0),
            'stock_quantity' => (int) ($row['maragi'] ?? $row['stock_quantity'] ?? 0),
            'min_stock_level' => (int) ($row['min_maragi'] ?? $row['min_stock_level'] ?? 0),
            'unit' => $row['erteuli'] ?? $row['unit'] ?? 'ცალი',
            'is_active' => $this->parseBoolean($row['aqtiuri'] ?? $row['is_active'] ?? true),
            'track_stock' => $this->parseBoolean($row['maragis_aghrichva'] ?? $row['track_stock'] ?? true),
            'description' => $row['aghtsera'] ?? $row['description'] ?? null,
            'user_id' => $this->userId,
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'dasaxeleba' => 'required_without:name|string|max:255',
            'name' => 'required_without:dasaxeleba|string|max:255',
            'kodi_sku' => 'nullable|string|max:100',
            'sku' => 'nullable|string|max:100',
            'fasi' => 'nullable|numeric|min:0',
            'price' => 'nullable|numeric|min:0',
            'sabithumo_fasi' => 'nullable|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'tvitghirebulebha' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'maragi' => 'nullable|integer|min:0',
            'stock_quantity' => 'nullable|integer|min:0',
            'min_maragi' => 'nullable|integer|min:0',
            'min_stock_level' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Parse price value
     */
    private function parsePrice($value)
    {
        if (is_string($value)) {
            // Remove currency symbols and spaces
            $value = preg_replace('/[^\d.,]/', '', $value);
            // Convert comma to dot for decimal
            $value = str_replace(',', '.', $value);
        }

        return (float) $value;
    }

    /**
     * Parse boolean value
     */
    private function parseBoolean($value)
    {
        if (is_string($value)) {
            $value = strtolower(trim($value));
            return in_array($value, ['კი', 'yes', 'true', '1', 'ki']);
        }

        return (bool) $value;
    }

    /**
     * Generate SKU from product name
     */
    private function generateSku($name)
    {
        $slug = Str::slug($name);
        $random = strtoupper(Str::random(4));
        return strtoupper(substr($slug, 0, 6)) . '-' . $random;
    }
}
