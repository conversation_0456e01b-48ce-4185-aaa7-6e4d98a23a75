<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Exports\ProductsExport;
use App\Imports\ProductsImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::with(['category', 'user'])
            ->when($request->search, function ($q, $search) {
                return $q->where('name', 'like', "%{$search}%")
                        ->orWhere('sku', 'like', "%{$search}%")
                        ->orWhere('barcode', 'like', "%{$search}%");
            })
            ->when($request->category_id, function ($q, $categoryId) {
                return $q->where('category_id', $categoryId);
            })
            ->when($request->is_active !== null, function ($q) use ($request) {
                return $q->where('is_active', $request->boolean('is_active'));
            })
            ->when($request->low_stock, function ($q) {
                return $q->lowStock();
            });

        $products = $query->orderBy($request->get('sort_by', 'created_at'), $request->get('sort_order', 'desc'))
                         ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sku' => 'nullable|string|unique:products,sku',
            'barcode' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'min_stock_level' => 'required|integer|min:0',
            'unit' => 'required|string|max:50',
            'category_id' => 'nullable|exists:categories,id',
            'is_active' => 'boolean',
            'track_stock' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['user_id'] = $request->user()->id;
        $data['slug'] = Str::slug($data['name']);

        // Generate SKU if not provided
        if (empty($data['sku'])) {
            $data['sku'] = $this->generateUniqueSku($data['name']);
        }

        $product = Product::create($data);
        $product->load(['category', 'user']);

        return response()->json([
            'success' => true,
            'message' => 'Product created successfully',
            'data' => $product
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $product = Product::with(['category', 'user'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $product
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sku' => 'nullable|string|unique:products,sku,' . $product->id,
            'barcode' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'min_stock_level' => 'required|integer|min:0',
            'unit' => 'required|string|max:50',
            'category_id' => 'nullable|exists:categories,id',
            'is_active' => 'boolean',
            'track_stock' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['slug'] = Str::slug($data['name']);

        $product->update($data);
        $product->load(['category', 'user']);

        return response()->json([
            'success' => true,
            'message' => 'Product updated successfully',
            'data' => $product
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $product = Product::findOrFail($id);
        $product->delete();

        return response()->json([
            'success' => true,
            'message' => 'Product deleted successfully'
        ]);
    }

    /**
     * Generate unique SKU
     */
    private function generateUniqueSku($name)
    {
        $prefix = strtoupper(substr($name, 0, 3));
        $counter = 1;

        do {
            $sku = $prefix . '-' . str_pad($counter, 4, '0', STR_PAD_LEFT);
            $counter++;
        } while (Product::where('sku', $sku)->exists());

        return $sku;
    }

    /**
     * Export products to Excel
     */
    public function export(Request $request)
    {
        $filename = 'products-' . now()->format('Y-m-d-H-i-s') . '.xlsx';

        return Excel::download(
            new ProductsExport($request->user()->id),
            $filename
        );
    }

    /**
     * Import products from Excel
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $import = new ProductsImport($request->user()->id);
            Excel::import($import, $request->file('file'));

            return response()->json([
                'message' => 'Products imported successfully'
            ]);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            return response()->json([
                'message' => 'Import validation failed',
                'errors' => $e->failures()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Import failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download import template
     */
    public function downloadTemplate()
    {
        $headers = [
            ['დასახელება', 'კოდი (SKU)', 'ბარკოდი', 'კატეგორია', 'ფასი', 'საბითუმო ფასი', 'თვითღირებულება', 'მარაგი', 'მინ. მარაგი', 'ერთეული', 'აქტიური', 'მარაგის აღრიცხვა', 'აღწერა'],
            ['Product Name', 'SKU Code', 'Barcode', 'Category', 'Price', 'Wholesale Price', 'Cost Price', 'Stock', 'Min Stock', 'Unit', 'Active (კი/არა)', 'Track Stock (კი/არა)', 'Description'],
            ['მაგალითი პროდუქტი', 'PROD-001', '1234567890123', 'ელექტრონიკა', '100.50', '85.00', '60.00', '50', '10', 'ცალი', 'კი', 'კი', 'მაგალითი აღწერა']
        ];

        $filename = 'products-import-template.xlsx';

        return Excel::download(new class($headers) implements \Maatwebsite\Excel\Concerns\FromArray {
            private $data;

            public function __construct($data) {
                $this->data = $data;
            }

            public function array(): array {
                return $this->data;
            }
        }, $filename);
    }
}
