<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class InvoiceController extends Controller
{
    use AuthorizesRequests;
    /**
     * Display a listing of invoices.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Invoice::with(['order', 'customer'])
            ->where('user_id', $request->user()->id);

        // Filter by status
        if ($request->has('status')) {
            $query->byStatus($request->status);
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('issue_date', '>=', $request->from_date);
        }

        if ($request->has('to_date')) {
            $query->whereDate('issue_date', '<=', $request->to_date);
        }

        // Search by invoice number or customer
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('company_name', 'like', "%{$search}%");
                  });
            });
        }

        $invoices = $query->recent()
            ->paginate($request->get('per_page', 15));

        return response()->json($invoices);
    }

    /**
     * Create invoice from order.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:orders,id',
            'due_date' => 'required|date|after:today',
            'notes' => 'nullable|string|max:1000',
            'company_details' => 'required|array',
            'company_details.name' => 'required|string',
            'company_details.address' => 'nullable|string',
            'company_details.phone' => 'nullable|string',
            'company_details.email' => 'nullable|email',
            'company_details.tax_number' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $order = Order::with(['items', 'customer'])->findOrFail($request->order_id);

        // Check if invoice already exists for this order
        if ($order->invoice) {
            return response()->json([
                'message' => 'Invoice already exists for this order',
                'invoice' => $order->invoice
            ], 409);
        }

        // Prepare customer details
        $customerDetails = [
            'name' => $order->customer->name ?? 'N/A',
            'email' => $order->customer->email ?? '',
            'phone' => $order->customer->phone ?? '',
            'company_name' => $order->customer->company_name ?? '',
        ];

        if ($order->billing_address) {
            $customerDetails['address'] = $order->billing_address;
        }

        $invoice = Invoice::create([
            'invoice_number' => (new Invoice())->generateInvoiceNumber(),
            'order_id' => $order->id,
            'user_id' => $request->user()->id,
            'customer_id' => $order->customer_id,
            'subtotal' => $order->subtotal,
            'tax_amount' => $order->tax_amount,
            'discount_amount' => $order->discount_amount,
            'total_amount' => $order->total_amount,
            'issue_date' => now(),
            'due_date' => $request->due_date,
            'notes' => $request->notes,
            'company_details' => $request->company_details,
            'customer_details' => $customerDetails,
        ]);

        return response()->json([
            'message' => 'Invoice created successfully',
            'invoice' => $invoice->load(['order', 'customer'])
        ], 201);
    }

    /**
     * Display the specified invoice.
     */
    public function show(Invoice $invoice): JsonResponse
    {
        if ($invoice->user_id !== request()->user()->id) {
            abort(403, 'Unauthorized');
        }

        return response()->json($invoice->load(['order.items', 'customer']));
    }

    /**
     * Update the specified invoice.
     */
    public function update(Request $request, Invoice $invoice): JsonResponse
    {
        if ($invoice->user_id !== $request->user()->id) {
            abort(403, 'Unauthorized');
        }

        $validator = Validator::make($request->all(), [
            'due_date' => 'sometimes|date',
            'notes' => 'nullable|string|max:1000',
            'status' => 'sometimes|in:draft,sent,paid,overdue,cancelled',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $invoice->update($request->only(['due_date', 'notes', 'status']));

        return response()->json([
            'message' => 'Invoice updated successfully',
            'invoice' => $invoice->load(['order', 'customer'])
        ]);
    }

    /**
     * Generate PDF for invoice.
     */
    public function generatePdf(Invoice $invoice): JsonResponse
    {
        if ($invoice->user_id !== request()->user()->id) {
            abort(403, 'Unauthorized');
        }

        try {
            $pdfPath = $invoice->generatePDF();

            return response()->json([
                'message' => 'PDF generated successfully',
                'pdf_url' => Storage::url($pdfPath),
                'pdf_path' => $pdfPath
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate PDF',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download PDF invoice.
     */
    public function downloadPdf(Invoice $invoice)
    {
        if ($invoice->user_id !== request()->user()->id) {
            abort(403, 'Unauthorized');
        }

        if (!$invoice->pdf_path || !Storage::exists($invoice->pdf_path)) {
            $invoice->generatePDF();
        }

        return Storage::download($invoice->pdf_path, 'invoice-' . $invoice->invoice_number . '.pdf');
    }

    /**
     * Mark invoice as paid.
     */
    public function markAsPaid(Invoice $invoice): JsonResponse
    {
        if ($invoice->user_id !== request()->user()->id) {
            abort(403, 'Unauthorized');
        }

        $invoice->markAsPaid();

        return response()->json([
            'message' => 'Invoice marked as paid',
            'invoice' => $invoice->fresh()
        ]);
    }

    /**
     * Remove the specified invoice.
     */
    public function destroy(Invoice $invoice): JsonResponse
    {
        if ($invoice->user_id !== request()->user()->id) {
            abort(403, 'Unauthorized');
        }

        // Delete PDF file if exists
        if ($invoice->pdf_path && Storage::exists($invoice->pdf_path)) {
            Storage::delete($invoice->pdf_path);
        }

        $invoice->delete();

        return response()->json([
            'message' => 'Invoice deleted successfully'
        ]);
    }
}
