<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Product extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'sku',
        'barcode',
        'price',
        'wholesale_price',
        'cost_price',
        'stock_quantity',
        'min_stock_level',
        'unit',
        'images',
        'is_active',
        'track_stock',
        'category_id',
        'user_id',
    ];

    protected function casts(): array
    {
        return [
            'price' => 'decimal:2',
            'wholesale_price' => 'decimal:2',
            'cost_price' => 'decimal:2',
            'is_active' => 'boolean',
            'track_stock' => 'boolean',
            'images' => 'array',
        ];
    }

    // Relationships
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeLowStock($query)
    {
        return $query->whereColumn('stock_quantity', '<=', 'min_stock_level');
    }

    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }

    // Accessors
    public function getIsLowStockAttribute()
    {
        return $this->track_stock && $this->stock_quantity <= $this->min_stock_level;
    }

    public function getIsOutOfStockAttribute()
    {
        return $this->track_stock && $this->stock_quantity <= 0;
    }

    public function getProfitMarginAttribute()
    {
        if (!$this->cost_price || $this->cost_price <= 0) {
            return null;
        }

        return (($this->price - $this->cost_price) / $this->cost_price) * 100;
    }

    // Mutators
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = $value;
        $this->attributes['slug'] = Str::slug($value);
    }

    // Methods
    public function generateSku()
    {
        $prefix = $this->category ? strtoupper(substr($this->category->name, 0, 3)) : 'PRD';
        $number = str_pad($this->id ?? Product::count() + 1, 4, '0', STR_PAD_LEFT);
        return $prefix . '-' . $number;
    }

    public function updateStock($quantity, $operation = 'subtract')
    {
        if (!$this->track_stock) {
            return true;
        }

        if ($operation === 'add') {
            $this->stock_quantity += $quantity;
        } else {
            $this->stock_quantity -= $quantity;
        }

        return $this->save();
    }
}
