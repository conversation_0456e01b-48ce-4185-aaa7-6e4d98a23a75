<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Barryvdh\DomPDF\Facade\Pdf as PDF;
use Illuminate\Support\Facades\Storage;

class Invoice extends Model
{
    protected $fillable = [
        'invoice_number',
        'order_id',
        'user_id',
        'customer_id',
        'status',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'issue_date',
        'due_date',
        'paid_date',
        'notes',
        'company_details',
        'customer_details',
        'pdf_path',
    ];

    protected function casts(): array
    {
        return [
            'subtotal' => 'decimal:2',
            'tax_amount' => 'decimal:2',
            'discount_amount' => 'decimal:2',
            'total_amount' => 'decimal:2',
            'issue_date' => 'date',
            'due_date' => 'date',
            'paid_date' => 'date',
            'company_details' => 'array',
            'customer_details' => 'array',
        ];
    }

    // Relationships
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['paid', 'cancelled']);
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('issue_date', 'desc');
    }

    // Methods
    public function generateInvoiceNumber()
    {
        $year = now()->format('Y');
        $month = now()->format('m');
        $count = static::whereYear('created_at', $year)
                      ->whereMonth('created_at', $month)
                      ->count() + 1;
        $number = str_pad($count, 4, '0', STR_PAD_LEFT);

        return 'INV-' . $year . $month . '-' . $number;
    }

    public function generatePDF()
    {
        $data = [
            'invoice' => $this,
            'order' => $this->order->load('items.product'),
            'company' => $this->company_details,
            'customer' => $this->customer_details,
        ];

        $pdf = PDF::loadView('invoices.template', $data);

        $filename = 'invoice-' . $this->invoice_number . '.pdf';
        $path = 'invoices/' . $filename;

        Storage::put($path, $pdf->output());

        $this->update(['pdf_path' => $path]);

        return $path;
    }

    public function markAsPaid()
    {
        $this->update([
            'status' => 'paid',
            'paid_date' => now(),
        ]);
    }

    public function isOverdue()
    {
        return $this->due_date < now() && !in_array($this->status, ['paid', 'cancelled']);
    }

    public function getDaysOverdue()
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return now()->diffInDays($this->due_date);
    }
}
