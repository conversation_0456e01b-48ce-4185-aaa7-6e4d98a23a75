<?php

namespace App\Exports;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProductsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $userId;

    public function __construct($userId = null)
    {
        $this->userId = $userId;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $query = Product::with('category');

        if ($this->userId) {
            $query->where('user_id', $this->userId);
        }

        return $query->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'დასახელება',
            'კოდი (SKU)',
            'ბარკოდი',
            'კატეგორია',
            'ფასი',
            'საბითუმო ფასი',
            'თვითღირებულება',
            'მარაგი',
            'მინ. მარაგი',
            'ერთეული',
            'აქტიური',
            'მარაგის აღრიცხვა',
            'აღწერა',
            'შექმნის თარიღი',
        ];
    }

    /**
     * @param Product $product
     * @return array
     */
    public function map($product): array
    {
        return [
            $product->id,
            $product->name,
            $product->sku,
            $product->barcode,
            $product->category ? $product->category->name : '',
            $product->price,
            $product->wholesale_price,
            $product->cost_price,
            $product->stock_quantity,
            $product->min_stock_level,
            $product->unit,
            $product->is_active ? 'კი' : 'არა',
            $product->track_stock ? 'კი' : 'არა',
            $product->description,
            $product->created_at->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }
}
