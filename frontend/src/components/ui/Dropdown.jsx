import React, { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

const Dropdown = ({ 
  trigger, 
  children, 
  align = 'right',
  width = 'w-56',
  className = ''
}) => {
  const alignmentClasses = {
    left: 'origin-top-left left-0',
    right: 'origin-top-right right-0',
    center: 'origin-top left-1/2 transform -translate-x-1/2'
  };

  return (
    <Menu as="div" className={`relative inline-block text-left ${className}`}>
      <div>
        <Menu.Button className="inline-flex w-full justify-center items-center">
          {trigger}
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items 
          className={`absolute z-10 mt-2 ${width} ${alignmentClasses[align]} rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none`}
        >
          <div className="py-1">
            {children}
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

const DropdownItem = ({ 
  children, 
  onClick, 
  href, 
  disabled = false,
  className = '',
  icon: Icon
}) => {
  const baseClasses = "group flex w-full items-center px-4 py-2 text-sm transition-colors";
  const enabledClasses = "text-gray-700 hover:bg-gray-100 hover:text-gray-900";
  const disabledClasses = "text-gray-400 cursor-not-allowed";
  
  const classes = `${baseClasses} ${disabled ? disabledClasses : enabledClasses} ${className}`;

  if (href) {
    return (
      <Menu.Item disabled={disabled}>
        {({ active }) => (
          <a
            href={href}
            className={`${classes} ${active ? 'bg-gray-100' : ''}`}
          >
            {Icon && <Icon className="mr-3 h-4 w-4" aria-hidden="true" />}
            {children}
          </a>
        )}
      </Menu.Item>
    );
  }

  return (
    <Menu.Item disabled={disabled}>
      {({ active }) => (
        <button
          type="button"
          className={`${classes} ${active ? 'bg-gray-100' : ''}`}
          onClick={onClick}
        >
          {Icon && <Icon className="mr-3 h-4 w-4" aria-hidden="true" />}
          {children}
        </button>
      )}
    </Menu.Item>
  );
};

const DropdownDivider = () => (
  <div className="border-t border-gray-100 my-1" />
);

Dropdown.Item = DropdownItem;
Dropdown.Divider = DropdownDivider;

export default Dropdown;
