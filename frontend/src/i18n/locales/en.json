{"common": {"welcome": "Welcome", "dashboard": "Dashboard", "products": "Products", "orders": "Orders", "customers": "Customers", "invoices": "Invoices", "reports": "Reports", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "name": "Name", "phone": "Phone", "address": "Address", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "price": "Price", "quantity": "Quantity", "status": "Status", "date": "Date", "actions": "Actions", "yes": "Yes", "no": "No", "active": "Active", "inactive": "Inactive", "back": "Back", "next": "Next", "previous": "Previous", "page": "Page", "of": "of", "loading": "Loading...", "no_data": "No data found", "confirm_delete": "Are you sure you want to delete this item?"}, "products": {"product": "Product", "product_name": "Product Name", "sku": "SKU", "barcode": "Barcode", "category": "Category", "wholesale_price": "Wholesale Price", "retail_price": "Retail Price", "cost_price": "Cost Price", "stock": "Stock", "min_stock": "Minimum Stock", "unit": "Unit", "description": "Description", "track_stock": "Track Stock", "new_product": "New Product", "edit_product": "Edit Product", "manage_products": "Manage your products", "add_product": "Add Product", "low_stock": "Low Stock", "out_of_stock": "Out of Stock", "in_stock": "In Stock"}, "orders": {"order": "Order", "order_number": "Order Number", "order_date": "Order Date", "customer": "Customer", "order_type": "Order Type", "retail": "Retail", "wholesale": "Wholesale", "order_status": "Order Status", "pending": "Pending", "confirmed": "Confirmed", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "new_order": "New Order", "edit_order": "Edit Order", "order_details": "Order Details", "order_items": "Order Items", "add_product": "Add Product", "shipping_address": "Shipping Address", "billing_address": "Billing Address", "notes": "Notes", "manage_orders": "Manage your orders", "order_info": "Order Information", "totals": "Totals", "generate_invoice": "Generate Invoice"}, "customers": {"customer": "Customer", "customer_name": "Customer Name", "company_name": "Company Name", "tax_number": "Tax Number", "city": "City", "postal_code": "Postal Code", "country": "Country", "new_customer": "New Customer", "edit_customer": "Edit Customer", "customer_details": "Customer Details", "order_history": "Order History", "total_orders": "Total Orders", "total_spent": "Total Spent", "last_order": "Last Order", "manage_customers": "Manage your customers and their information", "personal_info": "Personal Information", "company_info": "Company Information", "additional_info": "Additional Information", "contact": "Contact", "revenue": "Revenue"}, "invoices": {"invoice": "Invoice", "invoice_number": "Invoice Number", "issue_date": "Issue Date", "due_date": "Due Date", "paid_date": "Paid <PERSON>", "invoice_status": "Invoice Status", "draft": "Draft", "sent": "<PERSON><PERSON>", "paid": "Paid", "overdue": "Overdue", "generate_invoice": "Generate Invoice", "download_pdf": "Download PDF", "mark_as_paid": "<PERSON> as <PERSON><PERSON>", "manage_invoices": "Manage your invoices"}, "auth": {"login_title": "Sign in to your account", "register_title": "Create your account", "remember_me": "Remember me", "forgot_password": "Forgot your password?", "already_have_account": "Already have an account?", "dont_have_account": "Don't have an account?", "sign_in": "Sign in", "sign_up": "Sign up", "password_confirmation": "Confirm Password", "welcome_back": "Welcome back!", "create_account": "Create Account"}, "dashboard": {"welcome_message": "Welcome to your B2B Platform", "total_products": "Total Products", "low_stock_products": "Low Stock Products", "total_customers": "Total Customers", "recent_orders": "Recent Orders", "monthly_revenue": "Monthly Revenue", "quick_actions": "Quick Actions", "statistics": "Statistics", "overview": "Overview"}, "validation": {"required": "This field is required", "email_invalid": "Please enter a valid email address", "password_min": "Password must be at least 8 characters", "password_confirmation_mismatch": "Password confirmation does not match"}, "messages": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "operation_successful": "Operation completed successfully", "operation_failed": "Operation failed", "unauthorized": "Unauthorized access", "not_found": "Resource not found", "validation_failed": "Validation failed", "server_error": "Internal server error"}}